# 学习总结

**姓名：** 常一诺

**日期：** 2025年8月8日

## 目录

- [学习总结](#学习总结)
  - [目录](#目录)
  - [一、书籍电子文本化](#一书籍电子文本化)
    - [1.1 环境配置](#11-环境配置)
      - [1.1.1 Layout Detector](#111-layout-detector)
      - [1.1.2 Paddle](#112-paddle)
    - [1.2 测试环境](#12-测试环境)
    - [1.3 拆分PDF](#13-拆分pdf)
    - [1.4 完整提取](#14-完整提取)
  - [二、机器学习](#二机器学习)
    - [2.1 集成学习](#21-集成学习)
    - [2.2 聚类](#22-聚类)
  - [三、知识图谱](#三知识图谱)
    - [3.1 知识图谱推理](#31-知识图谱推理)
    - [3.2 知识图谱融合](#32-知识图谱融合)
  - [四、文献阅读](#四文献阅读)
    - [4.1 Knowledge Graph Embedding by Translating on Hyperplanes](#41-knowledge-graph-embedding-by-translating-on-hyperplanes)

## 一、书籍电子文本化

### 1.1 环境配置

在服务器上配置LayoutParser和Paddle-OCR运行环境的过程如下。

#### 1.1.1 Layout Detector

此环境的目标是运行layoutparser库，并调用基于Detectron2的、在大型文档布局数据集上预训练的模型，高精度定位页面中的表格、文本、标题等元素。

- 步骤1：创建并激活Conda环境
  - 首先创建一个名为layout_detector的Conda环境，指定Python版本为3.10：

    `conda create -y --name layout_detector python=3.10`
  - 激活该环境：

    `conda activate layout_detector`

    后续所有命令都在此环境中执行。
- 步骤2：安装核心依赖：PyTorch for GPU
  - Detectron2是基于PyTorch框架的。为了确保GPU能被正确、高效地利用，必须安装一个与服务器硬件（NVIDIA驱动所支持的CUDA版本）精确匹配的PyTorch版本。
  - 查询CUDA版本：

    `nvidia-smi`

    查询到服务器驱动支持CUDA12.2，根据官网上的信息，选择为CUDA12.1编译的PyTorch版本，以获得最佳的兼容性和性能。
  - 使用官方提供的conda命令安装PyTorch：

    `conda install -y pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia`
- 步骤3：安装Detectron2
  - Detectron2需要根据已安装的PyTorch和系统环境进行编译。官方推荐的最佳实践是直接从其GitHub仓库的源码进行安装。
  - 为避免潜在的编译错误，使用conda安装一个兼容的C++编译器：

    `conda install -y gxx_linux-64`
  - 安装Detectron2：

    `python -m pip install 'git+https://github.com/facebookresearch/detectron2.git'`
- 步骤4：安装上层API及辅助工具
  - 在核心框架安装完毕后，再安装上层的layoutparser库以及其他进行图像处理和可视化的标准工具。为了提升下载速度，使用国内的pip镜像源。
  - 安装 layoutparser：

    `pip install -i https://pypi.tuna.tsinghua.edu.cn/simple layoutparser`
  - 安装 opencv：

    `pip install -i https://pypi.tuna.tsinghua.edu.cn/simple opencv-python`
  - 安装 matplotlib：

    `pip install -i https://pypi.tuna.tsinghua.edu.cn/simple matplotlib`
  - 安装 pandas：

    `pip install -i https://pypi.tuna.tsinghua.edu.cn/simple pandas`
- 步骤5：注册为Jupyter Kernel
  - 安装ipykernel：

    `conda install -y -c conda-forge ipykernel`
  - 将当前环境注册为Jupyter Kernel：

    `python -m ipykernel install --user --name layout_detector --display-name "Layout Detector"`

#### 1.1.2 Paddle

此环境的目标是运行最新版的PaddleOCR。

- 步骤1：创建并激活Conda环境
  - 退出上个环境：

    `conda deactive layout_detector`
  - 创建新环境：

    `conda create -y --name paddle python=3.10`
  - 激活新环境：

    `conda activate paddle`
- 步骤2：安装核心依赖：PaddlePaddle for GPU
  - 根据官网上的信息，选择为CUDA11.8预编译的版本。
  - 安装PaddlePaddle：

    `pip install paddlepaddle-gpu==3.1.0.post118 -f https://www.paddlepaddle.org.cn/whl/linux/mkl/avx/stable.html`
- 步骤3：安装PaddleOCR及PDF处理工具
  - 安装pymupdf：

    `conda install -y -c conda-forge pymupdf`
  - 使用国内镜像源安装paddleocr：

    `pip install -i https://pypi.tuna.tsinghua.edu.cn/simple paddleocr`
- 步骤4：安装通用工具并注册Kernel
  - 安装通用工具：

    `pip install -i https://pypi.tuna.tsinghua.edu.cn/simple opencv-python matplotlib pandas ipykernel`
  - 注册为Jupyter Kernel：

    `python -m ipykernel install --user --name paddle --display-name "Paddle"`

之所以选择配置两个环境，是因为layoutparser所依赖的PyTorch和paddleocr所依赖的PaddlePaddle安装在同一个环境中会发生冲突。也许能以某种方法解决冲突，但是无法预料未来会出现的错误。所以选择了两个环境来安装，以保证两个流程都能正常运行。

### 1.2 测试环境

在配置好环境后，编写一个简单的脚本测试layoutparser是否能正常工作。代码如下：

```python
# test.py
import layoutparser as lp
import cv2

image = cv2.imread("./pictures/test_picture.jpg")
image = image[..., ::-1]

model = lp.Detectron2LayoutModel(
            config_path="/home/<USER>/my_models/layoutparser_models/publaynet_fasterrcnn/config.yml",
            model_path="/home/<USER>/my_models/layoutparser_models/publaynet_fasterrcnn/model_final.pth",
            label_map={0: "Text", 1: "Title", 2: "List", 3: "Table", 4: "Figure"},
            extra_config=["MODEL.ROI_HEADS.SCORE_THRESH_TEST", 0.8]
        )

layout = model.detext(image)

lp.draw_box(image, layout, box_width = 3)

type(layout)

layout[0]

text_block = lp.Layout([b for b in layout if b.type == 'Text'])
figure_blocks = lp.Layout([b for b in layout if b.type == 'Figure'])

text_blocks = lp.Layout([b for b in text_blocks if not any(b.is_in(b_fig) for b_fig in figure_blocks)])

h, w = image.shape[:2]

left_interval = lp.Interval(0, w/2*1.05, axis='x').put_on_canvas(image)

left_blocks = text_blocks.filter_by(left_interval, center=True)
left_blocks.sort(key = lambda b:b.coordinates[1], inplace=True)
right_blocks = lp.Layout([b for b in text_blocks if b not in left_blocks])
right_blocks.sort(key = lambda b:b.coordinates[1], inplace=True)

text_blocks = lp.Layout([b.set(id = idx) for idx, b in enumerate(left_blocks + right_blocks)])

lp.draw_box(image, text_blocks,
            box_width=3,
            show_element_id=True)
```

这个脚本的作用是使用layoutparser对一张图片进行布局解析，然后将解析结果可视化（使用不同颜色区分不同的布局元素）。

### 1.3 拆分PDF

由于原来编写的scanned.py脚本（针对扫描版PDF编写的脚本）在文字非常多、表格很少的PDF上表现很好，所以对于这类型的PDF仍然选择采用这个脚本进行处理。但是这个脚本有一个不太鲁棒的地方是：如果一个在PDF的处理过程中出现了错误，那么可能导致这一整个PDF的处理都出问题。为了使这个问题造成的影响小一些，在对PDF进行提取之前，先对PDF进行按章节的划分。

### 1.4 完整提取

使用scanned.py脚本对几本完整的扫描版PDF进行了处理，分别是：《ABX指南》、《临床微生物学检验技术》、《临床微生物学手册》上下册和《哈里森感染病学》。

目前只是大致检查了一下结果，接下来会对结果进行进一步的检查和处理。
## 二、机器学习

### 2.1 集成学习

集成学习（Ensemble Learning）是一种将多个学习器（Learners）组合在一起，以解决同一问题的机器学习方法。如果所用的模型类型相同，称为**同质集成**；若模型类型不同，则称为**异质集成**。在实际应用中，集成学习表现出非常优异的性能，例如过去十年中，几乎所有 KDDCup、Netflix 竞赛、 Kaggle 等机器学习竞赛的获胜方案都采用了集成技术。

想要得到一个性能优良的集成，核心在于：**个体学习器应当"好而不同"**（High accuracy and diversity）。多样性是集成学习成功的关键因素。理论上可以用**误差-分歧分解**（Error-Ambiguity Decomposition）来解释这一点：在回归问题且使用平方损失的情形下，该公式揭示了个体学习器的准确性与多样性对最终集成性能的影响。不过，由于"多样性"在数学上没有可操作的明确定义，该分解公式在实际中很难直接使用。
当个体学习器精度很高时，它们往往在预测上具有很强的相似性，这时就需要在精度和多样性之间进行权衡。

常见的集成学习方法可以分为两大类：

- **序列化方法（Sequential  Methods）**：学习器按顺序生成，每一步都依赖于前一步的结果，代表算法有 AdaBoost、Gradient Boosting、LPBoost 等。
- **并行化方法（Parallel Methods）**：各个学习器独立生成，最终将它们的结果组合在一起，代表算法有 Bagging、随机森林（Random Forest）、随机子空间（Random Subspace）等。

**Boosting**的核心思想是让后续学习器关注前一轮中预测错误的样本，通过不断迭代提升整体性能；而 **Bagging**则通过对训练集进行多次有放回的采样训练多个学习器，从而降低模型的方差并提升稳定性。

### 2.2 聚类

聚类（Clustering）是无监督学习中研究最深入、应用最广泛的任务之一。其目标是将数据样本划分为若干个通常不相交的簇（Cluster），使得簇内样本相似度高，而簇间样本相似度低。聚类既可以单独使用，用于探索数据的内在分布结构，也可以作为分类等其他学习任务的前驱步骤。

聚类性能度量（Validity Index）可分为：

- **外部指标（External Index）**：需要一个参考模型，将聚类结果与参考模型进行比较，例如  Jaccard 系数、Fowlkes--Mallows（FM）指数、Rand 指数。
- **内部指标（Internal Index）**：不依赖任何参考模型，直接基于数据分布评估聚类结果， 例如 Davies--Bouldin（DB）指数、Dunn 指数。

评价聚类好坏通常基于**簇内相似度高且簇间相似度低**这一原则。距离度量（Distance Metric）常用于衡量相似度，它需要满足非负性、同一性、对称性、三角不等式等基本性质。
对于数值型数据，可使用闵可夫斯基距离（Minkowski Distance）；对于无序离散属性，可使用 VDM （Value Difference
Metric）；对于混合属性，可使用 MinkovDM。

聚类结果的"好坏"并无绝对标准，最终依赖于使用者的目标与需求。
聚类是机器学习中新算法出现最快的领域之一，几乎总能找到一个新的评价标准，使得现有算法在其上表现不佳。

常见的聚类方法包括：

- **原型聚类**（Prototype-based Clustering）：以簇中心或原型为代表，如
  K-means。
- **密度聚类**（Density-based Clustering）：基于数据密度划分簇，如
  DBSCAN。
- **层次聚类**（Hierarchical Clustering）：构建层次化的簇结构，可分为凝聚型与分裂型。

## 三、知识图谱

### 3.1 知识图谱推理

知识图谱推理（Knowledge Graph
Reasoning）旨在基于已有的知识图谱，通过多种推理方式发现隐含的知识或推导出新的结论。常见的推理类型包括：

- **演绎推理**（Deductive Reasoning）
- **归纳推理**（Inductive Reasoning）
- **溯因推理**（Abductive Reasoning）
- **类比推理**（Analogical Reasoning）
- 本体推理、自然语言推理、视觉推理、模糊推理、非单调推理、不确定推理、贝叶斯推理等

从技术角度看，知识图谱推理主要包含以下研究方向：

- **向量表示学习（Representation Learning）与本体规则学习**：
  通过将实体、关系映射到低维向量空间中，实现嵌入式推理；结合本体规则可增强逻辑可解释性。
- **符号逻辑推理（Symbolic Logic Reasoning）**：
  依托规则演绎推理、符号推理解释，具有精确、可解释的优点，但扩展性受限。
- **融合方法**：
  将符号逻辑与向量表示学习相结合，以兼顾可解释性与数据驱动能力。

按实现方式分类：

- **基于Ontology的推理**：
  依赖本体结构进行逻辑推理，适用于知识表示精确度要求高的场景。
- **基于规则的推理**： 常见技术包括基于 OWL 的本体推理、基于 Datalog
  的规则推理以及基于 Rete 算法的产生式规则推理。
  优势是精确且可解释，劣势是在知识规模较大时鲁棒性与效率下降。
- **基于嵌入学习的推理**：
  将知识图谱嵌入到向量空间，通过向量运算进行推理，如
  TransE、RotatE、QuatE、BetaE、AnyBURL、DRUM、IterE 等模型。
- **基于规则学习的推理**：
  研究规则的自动学习方法，减少人工定义；结合嵌入学习可加速规则发现并捕捉隐含逻辑。
- **本体概念层嵌入（Ontology Embedding）**：
  将实体/关系、规则结构等映射到嵌入空间，实现更高层次的逻辑推理。

知识图谱可支持多种推理形式，演绎推理精确但扩展性不足，归纳推理依赖数据驱动，是当前研究热点。综合利用基于嵌入学习、规则学习及图神经网络等方法，能更好地提升推理能力。

> **思考：**
> 知识图谱推理是赋予知识"生命力"的关键。在我的项目中，从多本医学书籍中抽取出的是显性知识。例如，A书籍指出"细菌X"可引起"疾病Y"，B书籍指出"疾病Y"的典型症状是"症状Z"。通过推理技术，未来可以自动发现"细菌X"可能导致"症状Z"这样的隐含知识，这将极大提升构建后知识库的实用价值。基于嵌入学习的推理方法（如Trans系列）因其良好的扩展性，尤其适合处理大规模、数据驱动的医学知识。

### 3.2 知识图谱融合

知识图谱融合（Knowledge Graph
Fusion）旨在将多个来源、格式和结构不同的知识图谱合并为统一、一致、简洁的整体，为不同系统和应用之间的互操作性提供支持。
异构的根本原因在于**语言层面与模型层面的不匹配**。

- **概念层融合（本体匹配）**：
  侧重于识别模式层中等价或相似的类、属性或关系。
  常用方法包括基于术语匹配和基于结构匹配；大规模场景中常采用"先分块、再匹配"的方式。
- **实例层融合（实体对齐）**：
  将不同知识图谱中表示同一实体的节点对齐。近年来，表示学习技术逐渐被引入实体对齐过程，但缺乏专门为实体对齐设计的模型，精度仍有提升空间。
  人机协作是提升对齐效果的重要方向。
- **知识融合技术前沿**：
  - 多模态知识融合：融合来自图片、视频等不同模态的结构化知识
  - 公平高效的知识表示：为实体对齐设计专用的表示学习模型
  - 大规模知识融合：应对数十亿甚至千亿实体级别的高效融合算法需求

> **思考：**
> 知识融合是我的项目现阶段最直接的需求。我已经处理了《ABX指南》、《临床微生物学手册》等多本PDF，它们来自不同作者和出版社，对同一种细菌、药物或疾病的描述和命名可能存在差异（如同义词、缩写等）。因此，在将这些独立的知识源整合成一个统一的知识图谱时，实体对齐（实例层融合）将是必须解决的核心问题。本周学习的技术为后续消除知识冗余、建立实体间唯一链接提供了路线图。

## 四、文献阅读

### 4.1 Knowledge Graph Embedding by Translating on Hyperplanes

本文发表于 AAAI 2014，提出了 TransH 模型，旨在解决 TransE 在处理复杂关系映射（如一对多、多对一、多对多、反射关系）时的局限性。TransH 在保持模型简洁性的同时，引入了关系特定的超平面，使实体在不同关系中具有不同的表示，从而提升了嵌入的表达能力和预测准确性。
**一、模型动机与设计理念：**

- TransE
  假设所有关系都是实体之间的平移，但在面对复杂映射关系时容易导致实体嵌入冲突。
- TransH 为每个关系定义一个超平面（法向量 `W_r`）和一个平面内的平移向量
  `d_r`，实体在不同关系中被投影到对应超平面上，从而实现语义区分。
- 该设计允许实体在不同语义上下文中拥有不同的嵌入表示，有效缓解了 TransE
  的表示冲突问题。

**二、数学建模与评分函数：**

- 实体 `h` 和 `t` 被投影到关系 `r` 的超平面上，得到 `h⊥` 和 `t⊥`。
- 评分函数定义为：`f_r(h, t) = ||h⊥ + d_r - t⊥||²`，即在超平面上进行平移后，头实体应尽可能接近尾实体。
- 通过约束 `d_r` 与 `W_r` 正交，确保平移操作在超平面内进行。

**三、训练策略与负样本构造：**

- 采用基于间隔的排序损失函数，鼓励正确三元组的评分低于错误三元组。
- 引入 Bernoulli
  采样策略，根据关系的映射性质动态调整替换头或尾实体的概率，从而减少误标负例的风险。
- 优化采用随机梯度下降（SGD），并在每轮训练前对法向量进行单位归一化处理。

**四、实验结果与性能分析：**

- 在 WordNet 和 Freebase 数据集上，TransH 在链接预测任务中显著优于
  TransE，尤其在多对多和一对多关系上表现更为稳定。
- 在三元组分类任务中，TransH 在 WN11 和 FB15k
  上取得了最高准确率，展示了其强大的判别能力。
- 在事实抽取任务中，TransH 与文本模型结合后，在精确率和召回率上均优于
  TransE，说明其嵌入表示更具语义一致性。

**五、模型优势与未来方向：**

- **表达能力强：** 能够建模复杂关系映射，支持实体的多视角表示。
- **效率高：** 参数量与 TransE 相近，适用于大规模知识图谱。
- **泛化能力强：**
  在学习新关系时表现优异，仅需少量样本即可获得较好预测效果。
- **未来方向：** 可进一步结合文本信息、探索更高阶关系建模（如
  TransR、TransD），并应用于问答系统、推荐系统等下游任务。

总体而言，TransH 在保持模型简洁性的同时，显著提升了对复杂关系的建模能力，是知识图谱嵌入领域的重要进展。它不仅继承了 TransE 的高效性，还通过几何结构的改进实现了更强的表达力，为后续嵌入模型的发展提供了重要启示。
