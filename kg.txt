一、知识图谱概论
 人的大脑依靠所学的知识进行思考和推理，具有表示、获取、学习和处理知识  的能力是人类心智区别于其他物种最根本的区别之一
 语言是知识的最主要表示载体，语言与知识是实现认知智能最重要的两个方面
 知识图谱可以看作是一种结构化的知识表示方法，相比于文本更易于被机器查询和处理，因而在搜索引擎、智能问答、大数据分析等领域被广泛应用
 语言与知识的向量化表示，以及利用神经网络实现语言与知识的处理是重要的人工智能技术发展趋势
 
 知识表示与知识库-Knowledge Representation / Knowledge Base
 人工智能研究者陆续提出了大量知识表示的方法，如框架系统、产生式规则、描述逻辑等
 知识图谱两个核心基因：人工智能+互联网

 知识图谱技术源于互联网，最早落地应用的也是搜索引擎、智能问答和推荐计算等技术领域
 知识图谱通过规范化语义融合多来源数据，并能通过图谱推理能力支持复杂关联数据的挖掘分析，因此在大数据分析领域也有广泛应用
 不论是语言理解和视觉理解，外源知识库的引入都可以有力的提升语义理解的深度和广度
 知识图谱在医疗、金融、电商、通信等多个垂直领域都有着广泛的应用，并且每个领域都有其独特的实现和实践方式
 
 知识图谱不是单一技术，做知识图谱需要建立系统工程思维

二、知识图谱的表示
 符号表示+神经网络
 智能的精华是怎样实现推理，推理的关键是形式化逻辑
 “学习”需要我们清楚的知道要学习的是什么，那些认为任何东西都可以学习出来的想法是愚蠢的
 大脑中并没有符号和图像的存储，而是一些Big Activity Vectors之间相互发生作用

 知识表示与推理在人工智能的发展历史上一直居于核心位置
 传统的知识表示方法主要以符号表示为主，与知识图谱有关的典型方法有：描述逻辑、Horn Logic、产生式规则、框架系统、语义网络等等
 这些方法各有优缺点，但都有一个共同的缺点是知识的获取过程主要依靠专家和人工，越复杂的知识表示框架知识获取过程越困难

三、知识图谱的存储与查询
 属性图是工业界最常见的图谱建模方法，属性图数据库充分利用图结构特点做了性能优化，实用度高，但不支持符号推理 
 RDF是W3C推动的语义数据交换标准与规范，有更严格的语义逻辑基础，支持推理，并兼容更复杂的本体表示语言OWL
 在三元组无法满足语义表示需要时，OWL作为一种完备的本体语言，提供了更多可供选用的语义表达构件
 描述逻辑可以为知识图谱的表示与建模提供理论基础。描述逻辑之于知识图谱，好比关系代数之于关系数据库

存储方案	优点	缺点	代表性系统
三元组表	1.存储结构简单	1.大量自连接操作开销巨大	3store
水平表	1.知识图谱的邻接表，存储方案简单	1.可能超出所允许的表中列数目的上限 2.表中可能存在大量空值 3.无法表示一对多联系或多值属性 4.谓语的增加、修改或删除成本高	DLDB
属性表	1.克服了三元组表的自连接问题 2.解决了水平表中列数目过多的问题	1.真实知识图谱需建立的关系数量可能超过上限 2.由于知识图谱的灵活性，表中可能存在大量空值 3.无法表示一对多联系或多值属性	Jena
垂直划分	1.解决了空值问题 2.解决了多值问题 3.能够快速执行不同谓语表的连接查询	1.真实知识图谱需维护大量谓语表 2.复杂知识图谱查询需执行的表连接操作 3.数据更新维护代价大	SW-Store
六重索引	1.每种三元组模式查询均可直接使用对应索引快速查找 2.通过不同索引表之间的连接操作直接加速知识图谱上的连接查询	1.需要花费6倍的存储空间开销和数据更新维护代价 2.复杂知识图谱查询会产生大量索引表连接查询操作	RDF-3X Hexastore

 什么时候使用图数据库
高性能关系查询——需要快速遍历许多复杂关系的任何用例，这实际上包括欺诈检测，社交网络分析，网络和数据库基础设施等
模型的灵活性——任何依赖于添加新数据而不会中断现有查询池的用例，模型灵活性包括链接元数据，版本控制数据和不断添加新关系
快速和复杂的分析规则——当必须执行许多复杂规则时，例如子图的比较，这包括推荐、相似度计算和主数据管理

 知识图谱存储方式的选择需要综合考虑性能、动态扩展、实施成本等多方面综合因素
 区分原生图存储和非原生图存储：原生图存储在复杂关联查询和图计算方面有性能优势，非原生图存储兼容已有工具集通常学习和协调成本会低
 区分RDF图存储和属性图存储：RDF存储一般支持推理，属性图存储通常具有更好的图分析性能优势
 在大规模处理情况下，需要考虑与底层大数据存储引擎和上层图计算引擎集成需求
 图模型是更加接近人脑认知和自然语言的数据模型，图数据库是处理复杂的、半结构化、多维度的、紧密关联数据的最好技术，我们鼓励在知识图谱项目中采用和实践图数据库
 图数据库有它的弱处，假如你的应用场景不包含大量的关联查询，对于简单查询，传统关系模型和NoSQL数据库目前在性能方面更加有优势
 RDF作为一种知识图谱表示框架的参考标准，向上对接OWL等更丰富的语义表示和推理能力，向下对接简化后的属性图模型以及图计算引擎，是最值得重视的知识图谱表示框架

四、知识图谱的抽取与构建
 知识图谱≠专家系统
 知识图谱就是新一代的知识工程

 HMM算法
 鲍姆韦尔奇算法-EM算法
 维特比算法
 CRF条件随机场模型
 实体识别仍面临着标签分布不平衡，实体嵌套等问题，制约了现实应用
 中文的实体识别面临一些特有的问题，例如：中文没有自然分词、用字变化多、简化表达现象严重等
 实体识别是语义理解和构建知识图谱的重要一环，也是进一步抽取三元组和关系分类的前提基础

 基于模板的方法：基于触发词匹配的关系抽取；基于依存句法匹配的关系抽取
 基于监督学习的关系抽取：At-least-one Hypothesis
 机器学习框架：特征函数+最大熵模型；核函数；深度学习方法
 模板匹配、特征工程、远程监督、神经网络、降噪学习、预训练语言模型

 概念是人类在认识过程中，从感性认识上升到理性认识，把所感知的事物的共同本质特点抽象出来的一种表达
 概念知识一般可以通过基于模板、基于百科和基于序列标注等方法进行获取
 概念知识可以帮助自然语言理解，促进搜索、推荐等应用的效果

 基于模式匹配的方法在特定领域中性能较好，便于理解和后续应用，但对于语言、领域和文档形式都有不同程度的依赖，覆盖度和可移植性较差
 模式匹配的方法种，模板准确性是影响整个方法性能的重要因素，主要特点是高准确率低召回率
 事件抽取主要分为事件的发现和分类和事件要素抽取两部分，又可以细分为触发词识别与事件分类和要素检测与要素角色分类
 与关系抽取相比，事件抽取是一个更加困难和复杂的任务
 事件结构远比实体关系三元组复杂，事件中的Schema结构对事件抽取有很强的约束作用

 面对低资源少样本场景，我们需要更加智能的少样本、零样本知识抽取方法
 知识是不断变化的，我们需要能够终身学习知识的框架
