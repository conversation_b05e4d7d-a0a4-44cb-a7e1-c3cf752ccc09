# 学习总结

**姓名：** 常一诺

**日期：** 2025年8月8日

## 目录

- [学习总结](#学习总结)
  - [目录](#目录)
  - [一、书籍电子文本化](#一书籍电子文本化)
    - [1.1 环境配置](#11-环境配置)
      - [1.1.1 Layout Detector](#111-layout-detector)
      - [1.1.2 Paddle](#112-paddle)
    - [1.2 测试环境](#12-测试环境)
    - [1.3 拆分PDF](#13-拆分pdf)
    - [1.4 完整提取](#14-完整提取)
  - [二、机器学习](#二机器学习)
    - [2.1 集成学习](#21-集成学习)
    - [2.2 聚类](#22-聚类)
  - [三、知识图谱](#三知识图谱)
    - [3.1 知识图谱推理](#31-知识图谱推理)
    - [3.2 知识图谱融合](#32-知识图谱融合)
  - [四、文献阅读](#四文献阅读)
    - [4.1 Knowledge Graph Embedding by Translating on Hyperplanes](#41-knowledge-graph-embedding-by-translating-on-hyperplanes)

## 一、书籍电子文本化

### 1.1 环境配置

在服务器上配置LayoutParser和Paddle-OCR运行环境的过程如下。

#### 1.1.1 Layout Detector

此环境的目标是运行layoutparser库，并调用基于Detectron2的、在大型文档布局数据集上预训练的模型，高精度定位页面中的表格、文本、标题等元素。

**步骤1：创建并激活Conda环境**

首先创建一个名为layout_detector的Conda环境，指定Python版本为3.10：

```bash
conda create -y --name layout_detector python=3.10
```

激活该环境：

```bash
conda activate layout_detector
```

后续所有命令都在此环境中执行。

**步骤2：安装核心依赖：PyTorch for GPU**

Detectron2是基于PyTorch框架的。为了确保GPU能被正确、高效地利用，必须安装一个与服务器硬件（NVIDIA驱动所支持的CUDA版本）精确匹配的PyTorch版本。

查询CUDA版本：

```bash
nvidia-smi
```

查询到服务器驱动支持CUDA12.2，根据官网上的信息，选择为CUDA12.1编译的PyTorch版本，以获得最佳的兼容性和性能。

使用官方提供的conda命令安装PyTorch：

```bash
conda install -y pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia
```

**步骤3：安装Detectron2**

Detectron2需要根据已安装的PyTorch和系统环境进行编译。官方推荐的最佳实践是直接从其GitHub仓库的源码进行安装。

为避免潜在的编译错误，使用conda安装一个兼容的C++编译器：

```bash
conda install -y gxx_linux-64
```

安装Detectron2：

```bash
python -m pip install 'git+https://github.com/facebookresearch/detectron2.git'
```

**步骤4：安装上层API及辅助工具**

在核心框架安装完毕后，再安装上层的layoutparser库以及其他进行图像处理和可视化的标准工具。为了提升下载速度，使用国内的pip镜像源。

安装 layoutparser：

```bash
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple layoutparser
```

安装 opencv：

```bash
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple opencv-python
```

安装 matplotlib：

```bash
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple matplotlib
```

安装 pandas：

```bash
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple pandas
```

**步骤5：注册为Jupyter Kernel**

安装ipykernel：

```bash
conda install -y -c conda-forge ipykernel
```

将当前环境注册为Jupyter Kernel：

```bash
python -m ipykernel install --user --name layout_detector --display-name "Layout Detector"
```

#### 1.1.2 Paddle

此环境的目标是运行最新版的PaddleOCR。

**步骤1：创建并激活Conda环境**

退出上个环境：

```bash
conda deactivate layout_detector
```

创建新环境：

```bash
conda create -y --name paddle python=3.10
```

激活新环境：

```bash
conda activate paddle
```

**步骤2：安装核心依赖：PaddlePaddle for GPU**

根据官网上的信息，选择为CUDA11.8预编译的版本。

安装PaddlePaddle：

```bash
pip install paddlepaddle-gpu==3.1.0.post118 -f https://www.paddlepaddle.org.cn/whl/linux/mkl/avx/stable.html
```

**步骤3：安装PaddleOCR及PDF处理工具**

安装pymupdf：

```bash
conda install -y -c conda-forge pymupdf
```

使用国内镜像源安装paddleocr：

```bash
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple paddleocr
```

**步骤4：安装通用工具并注册Kernel**

安装通用工具：

```bash
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple opencv-python matplotlib pandas ipykernel
```

注册为Jupyter Kernel：

```bash
python -m ipykernel install --user --name paddle --display-name "Paddle"
```

之所以选择配置两个环境，是因为layoutparser所依赖的PyTorch和paddleocr所依赖的PaddlePaddle安装在同一个环境中会发生冲突。也许能以某种方法解决冲突，但是无法预料未来会出现的错误。所以选择了两个环境来安装，以保证两个流程都能正常运行。

### 1.2 测试环境

在配置好环境后，编写一个简单的脚本测试layoutparser是否能正常工作。代码如下：

```python
# test.py
import layoutparser as lp
import cv2

image = cv2.imread("./pictures/test_picture.jpg")
image = image[..., ::-1]

model = lp.Detectron2LayoutModel(
            config_path="/home/<USER>/my_models/layoutparser_models/publaynet_fasterrcnn/config.yml",
            model_path="/home/<USER>/my_models/layoutparser_models/publaynet_fasterrcnn/model_final.pth",
            label_map={0: "Text", 1: "Title", 2: "List", 3: "Table", 4: "Figure"},
            extra_config=["MODEL.ROI_HEADS.SCORE_THRESH_TEST", 0.8]
        )

layout = model.detect(image)

lp.draw_box(image, layout, box_width = 3)

type(layout)

layout[0]

text_blocks = lp.Layout([b for b in layout if b.type == 'Text'])
figure_blocks = lp.Layout([b for b in layout if b.type == 'Figure'])

text_blocks = lp.Layout([b for b in text_blocks if not any(b.is_in(b_fig) for b_fig in figure_blocks)])

h, w = image.shape[:2]

left_interval = lp.Interval(0, w/2*1.05, axis='x').put_on_canvas(image)

left_blocks = text_blocks.filter_by(left_interval, center=True)
left_blocks.sort(key = lambda b:b.coordinates[1], inplace=True)
right_blocks = lp.Layout([b for b in text_blocks if b not in left_blocks])
right_blocks.sort(key = lambda b:b.coordinates[1], inplace=True)

text_blocks = lp.Layout([b.set(id = idx) for idx, b in enumerate(left_blocks + right_blocks)])

lp.draw_box(image, text_blocks,
            box_width=3,
            show_element_id=True)
```

### 1.3 拆分PDF

使用scanned.py脚本对几本完整的扫描版PDF进行了处理，分别是：《ABX指南》、《临床微生物学检验技术》、《临床微生物学手册》上下册和《哈里森感染病学》。

目前只是大致检查了一下结果，接下来会对结果进行进一步的检查和处理。

### 1.4 完整提取

使用scanned.py脚本对几本完整的扫描版PDF进行了处理，分别是：《ABX指南》、《临床微生物学检验技术》、《临床微生物学手册》上下册和《哈里森感染病学》。

## 二、机器学习

### 2.1 集成学习

集成学习通过组合多个学习器来提升整体性能，是机器学习中的重要技术：

- **Bagging（装袋法）**：并行训练多个模型，通过投票或平均来决策，代表算法为随机森林
- **Boosting（提升法）**：串行训练，后续模型重点关注前面模型的错误，代表算法包括AdaBoost、梯度提升树
- **Stacking（堆叠法）**：使用元学习器来组合基学习器的预测结果

集成方法通常比单一模型具有更好的预测性能和鲁棒性。

### 2.2 聚类

聚类是无监督学习的重要任务，旨在将相似的数据点归为一类：

- **K-means聚类**：基于距离的划分聚类算法，需要预先指定簇的数量
- **DBSCAN**：基于密度的聚类算法，能够发现任意形状的簇并识别噪声点
- **层次聚类**（Hierarchical Clustering）：构建层次化的簇结构，可分为凝聚型与分裂型

## 三、知识图谱

### 3.1 知识图谱推理

知识图谱推理是从已有知识中发现新知识的过程，主要包括：

- **演绎推理**：基于逻辑规则进行推理，精确但扩展性有限
- **归纳推理**：基于统计学习方法，从数据中学习模式
- **基于嵌入的推理**：将实体和关系映射到低维向量空间进行推理
- **图神经网络推理**：利用图结构信息进行端到端学习

知识图谱可支持多种推理形式，演绎推理精确但扩展性不足，归纳推理依赖数据驱动，是当前研究热点。综合利用基于嵌入学习、规则学习及图神经网络等方法，能更好地提升推理能力。

> **思考：**
> 知识图谱推理是赋予知识"生命力"的关键。在我的项目中，从多本医学书籍中抽取出的是显性知识。例如，A书籍指出"细菌X"可引起"疾病Y"，B书籍指出"疾病Y"的典型症状是"症状Z"。通过推理技术，未来可以自动发现"细菌X"可能导致"症状Z"这样的隐含知识，这将极大提升构建后知识库的实用价值。基于嵌入学习的推理方法（如Trans系列）因其良好的扩展性，尤其适合处理大规模、数据驱动的医学知识。

### 3.2 知识图谱融合

知识图谱融合旨在整合来自不同数据源的知识，消除冗余和冲突：

- **模式层融合**：
  - 本体对齐：匹配不同知识图谱中的概念和关系
  - 模式映射：建立不同模式间的对应关系

- **实例层融合**：
  - 实体对齐：识别指向同一现实世界对象的不同实体
  - 实体链接：将提及链接到知识库中的标准实体

- **技术挑战**：
  - 异构性处理：不同数据源的结构、语言、粒度差异
  - 质量评估：数据源可靠性和一致性评估
  - 大规模知识融合：应对数十亿甚至千亿实体级别的高效融合算法需求

> **思考：**
> 知识融合是我的项目现阶段最直接的需求。我已经处理了《ABX指南》、《临床微生物学手册》等多本PDF，它们来自不同作者和出版社，对同一种细菌、药物或疾病的描述和命名可能存在差异（如同义词、缩写等）。因此，在将这些独立的知识源整合成一个统一的知识图谱时，实体对齐（实例层融合）将是必须解决的核心问题。本周学习的技术为后续消除知识冗余、建立实体间唯一链接提供了路线图。

## 四、文献阅读

### 4.1 Knowledge Graph Embedding by Translating on Hyperplanes

本文发表于 AAAI 2014，提出了 TransH 模型，旨在解决 TransE 在处理复杂关系映射（如一对多、多对一、多对多、反射关系）时的局限性。TransH 在保持模型简洁性的同时，引入了关系特定的超平面，使实体在不同关系中具有不同的表示，从而提升了嵌入的表达能力和预测准确性。

**核心创新**：

- **超平面投影机制**：为每个关系r定义一个超平面，通过法向量wr将头尾实体投影到该平面上
- **关系特定表示**：同一实体在不同关系中具有不同的投影表示，解决了TransE中实体表示固定的问题
- **几何直观性**：在超平面上进行平移操作，保持了TransE的几何解释性

**技术细节**：

- 投影操作：h⊥ = h - wr^T h wr，t⊥ = t - wr^T t wr
- 评分函数：fr(h,t) = ||h⊥ + dr - t⊥||²₂
- 约束条件：||wr||₂ = 1，dr^T wr = 0

**实验效果**：

- 在WordNet和Freebase数据集上显著优于TransE
- 特别在处理复杂关系类型时表现突出
- 计算复杂度仅略高于TransE，保持了良好的可扩展性

总体而言，TransH 在保持模型简洁性的同时，显著提升了对复杂关系的建模能力，是知识图谱嵌入领域的重要进展。它不仅继承了 TransE 的高效性，还通过几何结构的改进实现了更强的表达力，为后续嵌入模型的发展提供了重要启示。
